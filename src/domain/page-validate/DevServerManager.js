const { spawn } = require('child_process');
const chalk = require('chalk');

/**
 * DevServerManager - 开发服务器管理器
 *
 * 职责：
 * 1. 启动和停止开发服务器
 * 2. 检测服务器状态和端口
 * 3. 验证服务器可访问性
 * 4. 管理服务器生命周期
 */
class DevServerManager {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      port: 3000,
      devCommand: 'npm run dev',
      baseUrl: null,
      waitForServer: 60000,
      verbose: false,
      ...options
    };

    this.devServer = null;
    this.baseUrl = this.options.baseUrl || `http://localhost:${this.options.port}`;
    this.isServerReady = false;
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    if (this.options.baseUrl) {
      console.log(chalk.gray(`   使用外部服务器: ${this.baseUrl}`));
      this.isServerReady = true;
      return;
    }

    console.log(chalk.gray(`   启动开发服务器...`));

    return new Promise((resolve, reject) => {
      // 解析开发命令
      const [command, ...args] = this.options.devCommand.split(' ')

      this.devServer = spawn(command, args, {
        cwd: this.projectPath,
        stdio: 'pipe',
        env: {
          ...process.env,
          PORT: this.options.port.toString()
        }
      })

      let serverReady = false
      let output = ''
      let errorOutput = ''
      let detectedPort = null
      let compilationFinished = false

      // 监听输出判断服务器是否启动
      if (this.devServer.stdout) {
        this.devServer.stdout.on('data', (data) => {
          const text = data.toString()
          output += text

          if (this.options.verbose) {
            console.log(text)
          }

          // 检查编译是否完成（无论成功还是失败）
          if (text.includes('Failed to compile') || text.includes('ERROR in [eslint]')) {
            compilationFinished = true

            // 检查是否有编译错误
            if (text.includes('Failed to compile') ||
              text.includes('ERROR in [eslint]') ||
              (output + errorOutput).includes('Configuration for rule') ||
              (output + errorOutput).includes('should be number') ||
              (output + errorOutput).includes('should NOT have additional properties')) {

              if (this.options.verbose) {
                console.log(chalk.red(`   ❌ 检测到编译错误`))
              }

              // 等待一小段时间确保所有错误信息都被捕获
              setTimeout(() => {
                reject(new Error(`编译失败: 检测到 ESLint 或其他编译错误`))
              }, 2000)
              return
            }
          }

          // 检查服务器启动标志并提取端口
          if (!serverReady && (
            text.includes('Local:') ||
            text.includes('localhost:') ||
            text.includes('App running at') ||
            text.includes('Network:')
          )) {
            if (this.options.verbose) {
              console.log(chalk.blue(`   🔍 检测到服务器启动信息: ${text.trim()}`))
            }

            // 尝试提取端口号
            const portMatch = text.match(/localhost:(\d+)/)
            if (portMatch) {
              detectedPort = parseInt(portMatch[1])
              if (this.options.verbose) {
                console.log(chalk.gray(`   检测到服务器端口: ${detectedPort}`))
              }

              // 验证服务器是否真的可以访问
              this.verifyServerWithRetry(detectedPort, resolve, reject)
            }
          }
        })
      }

      if (this.devServer.stderr) {
        this.devServer.stderr.on('data', (data) => {
          const text = data.toString()
          errorOutput += text

          if (this.options.verbose) {
            console.error(chalk.red(text))
          }

          // 检查 stderr 中的编译错误
          if (text.includes('ERROR in [eslint]') || text.includes('Failed to compile')) {
            compilationFinished = true

            if (this.options.verbose) {
              console.log(chalk.red(`   ❌ 在 stderr 中检测到编译错误`))
            }

            // 等待一小段时间确保所有错误信息都被捕获
            setTimeout(() => {
              reject(new Error(`编译失败: ${text.trim()}`))
            }, 2000)
          }
        })
      }

      this.devServer.on('error', (error) => {
        reject(new Error(`启动开发服务器失败: ${error.message}`))
      })

      this.devServer.on('exit', (code) => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`开发服务器异常退出，代码: ${code}`))
        }
      })

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error(`开发服务器启动超时 (${this.options.waitForServer}ms)`))
        }
      }, this.options.waitForServer)
    });
  }

  /**
   * 带重试的服务器验证
   */
  async verifyServerWithRetry(port, resolve, reject, attempt = 1, maxAttempts = 5) {
    const delay = attempt * 2000; // 递增延迟：2s, 4s, 6s, 8s, 10s

    setTimeout(async () => {
      try {
        const isReady = await this.verifyServerReady(port);

        if (isReady) {
          this.options.port = port; // 更新实际端口
          this.baseUrl = `http://localhost:${port}`;
          this.isServerReady = true;

          if (this.options.verbose) {
            console.log(chalk.green(`   ✅ 服务器验证成功 (端口: ${port}, 尝试: ${attempt}/${maxAttempts})`));
          }
          resolve();
        } else if (attempt < maxAttempts) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`   ⚠️  端口 ${port} 验证失败，${delay/1000}秒后重试 (${attempt}/${maxAttempts})...`));
          }
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        } else {
          if (this.options.verbose) {
            console.log(chalk.red(`   ❌ 服务器验证失败，已达到最大重试次数 (${maxAttempts})`));
          }
          // 最后一次尝试失败，但不要 reject，让超时处理
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`   ⚠️  服务器验证出错 (尝试 ${attempt}/${maxAttempts}): ${error.message}`));
        }
        if (attempt < maxAttempts) {
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        }
      }
    }, delay);
  }

  /**
   * 检查输出中是否包含编译错误
   */
  hasCompilationError(output) {
    const errorPatterns = [
      /Failed to compile/,
      /ERROR in \[eslint\]/,
      /Configuration for rule .* is invalid/,
      /Value .* should be number/,
      /should NOT have additional properties/,
      /should match exactly one schema in oneOf/,
      /Failed to load config/,
      /Failed to load plugin/
    ];

    return errorPatterns.some(pattern => pattern.test(output));
  }

  /**
   * 验证服务器是否真的可以访问
   */
  async verifyServerReady(port) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://localhost:${port}`, {
        timeout: 5000,
        validateStatus: () => true // 接受任何状态码
      });

      return response.status < 500; // 只要不是服务器错误就认为可用
    } catch (error) {
      return false;
    }
  }

  /**
   * 停止开发服务器
   */
  async stopDevServer() {
    if (this.devServer && !this.devServer.killed) {
      this.devServer.kill('SIGTERM');

      // 等待进程结束
      await new Promise((resolve) => {
        this.devServer.on('exit', resolve);
        setTimeout(resolve, 5000); // 5秒超时
      });

      this.isServerReady = false;
    }
  }

  /**
   * 获取服务器状态
   */
  getServerStatus() {
    return {
      isReady: this.isServerReady,
      baseUrl: this.baseUrl,
      port: this.options.port,
      pid: this.devServer?.pid
    };
  }

  /**
   * 获取基础URL
   */
  getBaseUrl() {
    return this.baseUrl;
  }

  /**
   * 检查服务器是否运行中
   */
  isRunning() {
    return this.isServerReady && this.devServer && !this.devServer.killed;
  }
}

module.exports = DevServerManager;
